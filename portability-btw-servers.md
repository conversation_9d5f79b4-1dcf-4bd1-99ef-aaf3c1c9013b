🔄 Portability Between Servers
When moving to a new server:

Copy .env, docker-compose.yml, and volume contents.

Recreate volumes with:

bash
Copy
Edit
docker volume create --name supabase-data
docker volume create --name n8n-data
Use docker run --rm -v <volume-name>:/volume -v $(pwd):/backup alpine tar czf /backup/volume.tar.gz -C /volume . to back up and restore.

Run docker compose up -d — your data and environment will persist.