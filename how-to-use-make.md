Open terminal in your project folder and run any of the following:

Command	What It Does
make up	Starts your n8n + Supabase stack
make down	Stops all containers
make restart	Restarts all containers
make backup	Backs up both volumes to ./backups
make restore	Restores volumes from backup
make clean-volumes	Deletes both named volumes

💡 Pro Tip: Add --build to make up if you've changed Dockerfile content or .env.

📦 What This Enables
🚀 Easy setup/teardown/restore when migrating servers.

🔄 Reliable volume-level backups of both n8n and Supabase.

🧹 Cleanup and recreate volumes cleanly if needed.

🔒 Secure handling: no hardcoded passwords/scripts — uses .env.